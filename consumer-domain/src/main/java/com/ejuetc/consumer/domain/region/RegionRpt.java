package com.ejuetc.consumer.domain.region;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.consumer.api.dto.RegionDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.support.JpaRepositoryImplementation;

import java.util.List;
import java.util.Map;

import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static com.ejuetc.consumer.domain.commons.AMapUtils.makeUrl;

public interface RegionRpt extends JpaRepositoryImplementation<Region, Long> {

    @Cacheable("RegionRpt.findByAmapAdcode")
    Region findByAmapAdcode(String amapCode);

    @Cacheable("RegionRpt.findByAmapCitycode")
    Region findByAmapCitycode(String amapCitycode);

    @Query("""
            select r
            from Region r
            where r.businessCodes is not null
            """)
    @Cacheable("RegionRpt.findByBusinessOpen")
    List<Region> findByBusinessOpen();

    @Cacheable("RegionRpt.findByCode")
    Region findByCode(String code);

    @Cacheable("RegionRpt.findByType")
    List<Region> findByType(RegionDTO.Type type);

    default Region findRegion(String cityName) {
        String regionReq = makeUrl(
                "https://restapi.amap.com/v3/config/district"
                , Map.of(
                        "subdistrict", "0",
                        "keywords", cityName
                ));
        JSONObject regionResp = callAMap(regionReq);
        JSONArray districts = regionResp.getJSONArray("districts");
        for (int i = 0; i < districts.size(); i++) {
            JSONObject district = districts.getJSONObject(i);
            if ("city".equals(district.getString("level"))) {
                return findByAmapCitycode(district.getString("citycode"));
            }
        }

        throw new BusinessException("bc.ejuetc.consumer.1017", cityName);
    }

    @Query("""
            select r
            from Region r
            where (:parentCode is null or r.parent.code = :parentCode)
            and r.type = :type
            """)
    List<Region> findByParentCodeAndType(String parentCode, RegionDTO.Type type);

    @Query("""
            select r
            from Region r
            where (:parentRegionId is null or r.parent.id = :parentRegionId)
            and (:parentCityId is null or r.parentCity.id = :parentCityId)
            and (:type is null or r.type = :type)
            and (:keyword is null or r.name like concat('%',:keyword,'%'))
            """)
    List<Region> findList(Long parentRegionId, Long parentCityId, RegionDTO.Type type, String keyword);

    @Query("""
            select r
            from Region r
            where (:parentRegionId is null or r.parent.id = :parentRegionId)
            and (:parentCityId is null or r.parentCity.id = :parentCityId)
            and (:type is null or r.type = :type)
            and (:keyword is null or r.name like concat('%',:keyword,'%'))
            and (:featureCode is null or string_contains(r.featureCodes, :featureCode) = true)
            """)
    List<Region> findList(Long parentRegionId, Long parentCityId, RegionDTO.Type type, String keyword, RegionDTO.FeatureCode featureCode);
}
