package com.ejuetc.consumer.domain.region;

import com.alibaba.fastjson.JSONObject;
import com.ejuetc.channel.dto.BusinessOpenDTO;
import com.ejuetc.commons.base.entity.BaseEntity;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.ListUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.MapContent;
import com.ejuetc.consumer.api.dto.RegionDTO;
import jakarta.persistence.*;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.*;
import org.hibernate.annotations.Parameter;

import java.util.*;

import static com.ejuetc.commons.base.utils.StringUtils.*;
import static com.ejuetc.consumer.api.dto.RegionDTO.Type.TOWN;
import static com.ejuetc.consumer.domain.commons.AMapUtils.callAMap;
import static java.util.stream.Collectors.toMap;

@Getter
@Entity
@NoArgsConstructor
@Comment("地区")
@Table(name = "tb_region")
@Where(clause = "logic_delete = 0")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Region extends BaseEntity<Region> {

    @Id
    @GeneratedValue(generator = "region_id")
    @SequenceGenerator(name = "region_id", sequenceName = "seq_region_id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Comment("地区类型")
    @Column(name = "type")
    private RegionDTO.Type type;

    @Comment("闲鱼业务类型")
    @Column(name = "xy_busi_type")
    @Enumerated(EnumType.STRING)
    private RegionDTO.XyBusiType xyBusiType;

    @Column(name = "code", columnDefinition = "varchar(63) COMMENT '国标编码'")
    private String code;

    @Column(name = "amap_adcode", columnDefinition = "varchar(63) COMMENT '高德AD编码'")
    private String amapAdcode;

    @Column(name = "amap_citycode", columnDefinition = "varchar(63) COMMENT '高德城市编码'")
    private String amapCitycode;

    @Column(name = "name", columnDefinition = "varchar(255) COMMENT '全称'")
    private String name;

    @Column(name = "pinyin_full", columnDefinition = "varchar(255) COMMENT '全拼'")
    private String pinyinFull;

    @Column(name = "pinyin_initials", columnDefinition = "varchar(63) COMMENT '拼音首字母'")
    private String pinyinInitials;

    @Column(name = "pinyin_first", columnDefinition = "varchar(63) COMMENT '拼音第一个字母'")
    private String pinyinFirst;

    @Column(name = "short_name", columnDefinition = "varchar(255) COMMENT '简称'")
    private String shortName;

    @Column(name = "alias", columnDefinition = "text COMMENT '别名'")
    protected String alias;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", columnDefinition = "bigint(20) COMMENT '所属上级地区'")
    private Region parent;

    @Column(name = "parent_name", columnDefinition = "varchar(255) COMMENT '上级地区名称'")
    private String parentName;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Region> children = new ArrayList<>();

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_city_id", columnDefinition = "bigint(20) COMMENT '所属城市ID'")
    private Region parentCity;

//    @Column(name = "city_name", columnDefinition = "varchar(255) COMMENT '所属城市名称'")
//    private String cityName;

//    @OneToMany(mappedBy = "city", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    private List<Region> cityChildren = new ArrayList<>();

    @Type(value = ListUT.class, parameters = @Parameter(name = "elementType", value = "com.ejuetc.channel.dto.BusinessOpenDTO$Code"))
    @Column(name = "business_codes", columnDefinition = "varchar(127) COMMENT '开放业务编码'")
    private List<BusinessOpenDTO.Code> businessCodes = new ArrayList<>();

    @Type(value = ListUT.class, parameters = @Parameter(name = "elementType", value = "com.ejuetc.consumer.api.dto.RegionDTO$FeatureType"))
    @Column(name = "feature_codes", columnDefinition = "varchar(255) COMMENT '支持的功能编码'")
    private List<RegionDTO.FeatureType> featureCodes = new ArrayList<>();

    @Type(MapContentUT.class)
    @Column(name = "gov_verify_field_map", columnDefinition = "varchar(255) COMMENT '政府核验字段配置'")
    private MapContent govVerifyFieldMap;

    public Long getCityId() {
        return getCity() != null ? getCity().getId() : null;
    }

    private Region getCity() {
        return switch (type) {
            case CITY -> this;
            case DISTRICT, BUSI -> getParent();
            case TOWN -> getParent().isParentCity() ? getParent() : getParent().getParent();
            case PROVINCE -> throw new CodingException("省份[%s]无法返回所属城市", name);
        };
    }

    public boolean isParentCity() {
        return type == RegionDTO.Type.CITY;
    }

    public boolean isDistrict() {
        return type == RegionDTO.Type.DISTRICT;
    }

    public String getCityName() {
        return getCity() != null ? getCity().getName() : null;
    }


    private boolean hasAlias(String name) {
        return alias.contains(name);
    }

    public String getFullName() {
        return parent != null && !parent.equals(this) ? parent.getFullName() + name : name;
    }

    public void refreshPinyin() {
        this.pinyinFull = getFullPinyin(shortName);
        this.pinyinInitials = getInitials(shortName);
        this.pinyinFirst = pinyinInitials.charAt(0) + "";
    }

    public List<Region> getChildren(RegionDTO.Type type) {
        return type != null
                ? children.stream().filter(r -> r.isType(type)).toList()
                : children;
    }

    private boolean isType(RegionDTO.Type type) {
        return this.type == type;
    }

//    public void insertTown() {
//        if (cityChildren.stream().anyMatch(r -> r.getType() == TOWN)) return;
//
//        String regionReq = makeUrl("https://restapi.amap.com/v3/config/district", Map.of(
//                "subdistrict", "3",
//                "keywords", name
//        ));
//        JSONObject regionResp = callAMap(regionReq);
//        JSONObject city = regionResp.getJSONArray("districts").getJSONObject(0);
//        if (city.getString("level").equals("province"))
//            city = city.getJSONArray("districts").getJSONObject(0);
//        city.getJSONArray("districts").forEach(district -> {
//            JSONObject district1 = (JSONObject) district;
//            switch (district1.getString("level")) {
//                case "district" -> district1.getJSONArray("districts").forEach(street -> {
//                    createStreet((JSONObject) street);
//                });
//                case "street" -> createStreet(district1);
//            }
//        });
//    }

    private void createStreet(JSONObject street) {
        Region region = new Region();
        region.type = TOWN;
        region.name = street.getString("name");
        region.shortName = street.getString("name");
        region.alias = street.getString("name");
        region.setParent(getChildrenByAdcode(street.getString("adcode")));
    }

    private void setParent(Region parent) {
        this.parent = parent;
        this.parentName = parent.name;
        this.parent.children.add(this);

//        this.city = parent.getCity();
//        this.cityName = city.getName();
//        this.cityChildren.add(this);
    }

    private Region getChildrenByAdcode(String adcode) {
        if (this.amapAdcode.equals(adcode)) return this;
        return children.stream().filter(r -> r.amapAdcode.equals(adcode)).findFirst().orElseThrow(() -> new CodingException("未找到adcode为[%s]的地区", adcode));
    }

    public List<String> getGovVerifyFields(BusinessOpenDTO.Code businessCode) {
        return govVerifyFieldMap != null
                ? splitString(govVerifyFieldMap.getString(businessCode.name()))
                : Collections.emptyList();
    }

    public boolean support(BusinessOpenDTO.Code businessCode) {
        return businessCodes.contains(businessCode);
    }

    public boolean supportFeature(RegionDTO.FeatureCode featureCode) {
        return featureCodes.contains(featureCode);
    }

}
