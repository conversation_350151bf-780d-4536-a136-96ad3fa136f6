package com.ejuetc.consumer.impl;

import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class RegionFeatureTest {

    @Autowired
    private RegionRpt regionRpt;

    @Test
    public void testAllCities() {
        // 测试查询所有城市
        List<Region> regions = regionRpt.findByType(RegionDTO.Type.CITY);
        System.out.println("查询所有城市数量: " + regions.size());
        for (Region region : regions.subList(0, Math.min(5, regions.size()))) {
            System.out.println("城市: " + region.getName());
        }
    }

    @Test
    public void testQueryWithFeatureCode() {
        // 直接使用SQL查询来测试
        System.out.println("测试功能编码查询...");

        // 查询所有城市
        List<Region> allCities = regionRpt.findByType(RegionDTO.Type.CITY);
        System.out.println("总城市数量: " + allCities.size());

        // 查询前几个城市的名称
        for (int i = 0; i < Math.min(10, allCities.size()); i++) {
            Region city = allCities.get(i);
            System.out.println("城市: " + city.getName());
        }
    }
}
