package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.channel.dto.ChannelDTO;
import com.ejuetc.commons.base.component.OssComponent;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.delegation.EditDelegationPO;
import com.ejuetc.consumer.api.delegation.MediaPO;
import com.ejuetc.consumer.api.dto.DelegationDTO;
import com.ejuetc.consumer.web.vo.DelegationVO;
import lombok.SneakyThrows;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static com.ejuetc.channel.dto.BusinessOpenDTO.Code.RENT;
import static com.ejuetc.channel.dto.ChannelDTO.Code.XIANYU;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Level.BROKER;
import static com.ejuetc.consumer.api.dto.DelegationDTO.Status.DOWN_SUCC;
import static java.lang.Thread.sleep;
import static java.time.LocalDateTime.now;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DelegationApiTest {

    @Autowired
    private DelegationApiImpl delegationApi;
//    private DelegationAPI delegationApi = getAPI(DelegationAPI.class, "http://ejuetc-consumer.ejucloud.cn");
//    private DelegationAPI delegationApi = getAPI(DelegationAPI.class, "http://ejuetc-consumer.uat.ejucloud.cn");
//    private DelegationAPI delegationApi = getAPI(DelegationAPI.class, "http://localhost:8097");

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    //    @Autowired
    private OssComponent ossComponent;

    private static @NotNull List<MediaPO> getMedias() {
        return List.of(
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_2307031720a9fe142488e77bfdd48d9a00dc6d5d4e2ef555?x-oss-process=image/resize,m_mfit,w_1080,h_1080,limit_0/quality,q_85/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("厨房")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_23070317bd4b9554c222de2e69efb29aa8cae879789166ca?x-oss-process=image/resize,m_mfit,w_1080,h_1080,limit_0/quality,q_85/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卫生间")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("实景图")
                        .setName("卧室")
                ,
                new MediaPO()
                        .setUrl("https://fyoss-test.fangyou.com/jpg_21122717e1772984d9e7ab7bd26344da40db72a70a195a4d?x-oss-process=image/resize,m_mfit,w_1080,h_1080/quality,q_70/format,jpg")
                        .setType(MediaPO.Type.IMAGE)
                        .setSubtype("户型图")
                        .setName("卧室")
        );
    }

    @Test
    public void testQuery() {
        queryDomainApi.query(new DelegationDTO());
    }

    @Test
    public void add() {
        delegationApi.edit(1741957803503L, JSON.parseObject("""
                {
                	"buildingArea":80,
                	"checkinDate":"2025-03-10",
                	"type":"RENT",
                	"completionTime":"2010",
                	"roomNum":"101",
                	"currentFloorNum":"",
                	"freshCompanyDelegation":true,
                	"housePlanPurpose":"住宅",
                	"communityName":"花园府",
                	"estateImage":[],
                	"roomCount":1,
                	"priceUnit":100,
                	"unitName":"1单元",
                	"orient":"朝南",
                	"boutique":false,
                	"level":"BROKER",
                	"community":{
                		"townName":"笕桥街道",
                		"address":"明月桥路与花园兜街交叉口东北120米",
                		"districtName":"上城区",
                		"cityCode":"330100",
                		"latitude":30.301819,
                		"typeName":"商务住宅;住宅区;住宅小区",
                		"cityId":330100,
                		"around":{
                			"bus":[
                				{
                					"typecode":"150700",
                					"address":"(停运)社区接驳巴士/社区便利巴士1601M路;1903路/1903M路社区接驳巴士;335路;35路;81路;8212路",
                					"distance":217,
                					"name":"同协南路天城路口(公交站)",
                					"location":"120.222949,30.302442",
                					"id":"BV10180971",
                					"type":"交通设施服务;公交车站;公交车站相关"
                				},
                				{
                					"typecode":"150700",
                					"address":"1904M路社区便利巴士;1914M高峰接驳线",
                					"distance":314,
                					"name":"天城路明月桥路口(公交站)",
                					"location":"120.219644,30.299180",
                					"id":"BV11455808",
                					"type":"交通设施服务;公交车站;公交车站相关"
                				}
                			],
                			"kindergarten":[
                				{
                					"typecode":"141204",
                					"address":"花园兜街28号",
                					"distance":233,
                					"name":"杭州市笕桥花园幼儿园花园园区",
                					"location":"120.218535,30.301065",
                					"id":"B0FFGYAIQU",
                					"type":"科教文化服务;学校;幼儿园"
                				}
                			],
                			"types":[
                				"公交",
                				"学校",
                				"医院",
                				"购物",
                				"餐饮"
                			],
                			"subway":[],
                			"restaurant":[
                				{
                					"typecode":"050000",
                					"address":"笕桥街道盈澜商务中心1幢底商7、8",
                					"distance":203,
                					"name":"建德佬(盈澜商务中心1幢店)",
                					"location":"120.221882,30.300248",
                					"id":"B0G11CDCSR",
                					"type":"餐饮服务;餐饮相关场所;餐饮相关"
                				},
                				{
                					"typecode":"050300",
                					"address":"盈澜商务中心1幢",
                					"distance":217,
                					"name":"尊宝比萨(天城路店)",
                					"location":"120.221446,30.299947",
                					"id":"B0H0XC5IB7",
                					"type":"餐饮服务;快餐厅;快餐厅"
                				},
                				{
                					"typecode":"050100",
                					"address":"盈澜商务中心西北门西南100米",
                					"distance":247,
                					"name":"林徐记(盈澜商务中心1幢店)",
                					"location":"120.221153,30.299613",
                					"id":"B0FFKS83V6",
                					"type":"餐饮服务;中餐厅;中餐厅"
                				},
                				{
                					"typecode":"050300",
                					"address":"明月桥路344号",
                					"distance":267,
                					"name":"华姐柳州螺蛳粉(明月桥路店)",
                					"location":"120.221201,30.299445",
                					"id":"B0G0UDNQR5",
                					"type":"餐饮服务;快餐厅;快餐厅"
                				},
                				{
                					"typecode":"050700",
                					"address":"花园兜街43号",
                					"distance":287,
                					"name":"一鸣真鲜奶吧(杭州江干花园兜街奶吧)",
                					"location":"120.218388,30.300299",
                					"id":"B0FFJ7IOUY",
                					"type":"餐饮服务;冷饮店;冷饮店"
                				},
                				{
                					"typecode":"050000",
                					"address":"花园兜街36号",
                					"distance":289,
                					"name":"切果堂(东站店)",
                					"location":"120.218159,30.300575",
                					"id":"B0H1NC9Q5S",
                					"type":"餐饮服务;餐饮相关场所;餐饮相关"
                				}
                			],
                			"pharmacy":[
                				{
                					"typecode":"090601",
                					"address":"环站北路31号",
                					"distance":121,
                					"name":"老百姓大药房(环站北路店)",
                					"location":"120.220454,30.302867",
                					"id":"B0J1YMIYML",
                					"type":"医疗保健服务;医药保健销售店;药房"
                				},
                				{
                					"typecode":"090601",
                					"address":"铂瑞悦府底商12号",
                					"distance":311,
                					"name":"九洲全诺大药房铂瑞悦府店",
                					"location":"120.220230,30.299068",
                					"id":"B0JBSZN4TT",
                					"type":"医疗保健服务;医药保健销售店;药房"
                				}
                			],
                			"primarySchool":[
                				{
                					"typecode":"141203",
                					"address":"明月桥路399号",
                					"distance":217,
                					"name":"杭州市笕桥花园小学",
                					"location":"120.218543,30.301787",
                					"id":"B0FFGYAIQT",
                					"type":"科教文化服务;学校;小学"
                				}
                			],
                			"hospital":[
                				{
                					"typecode":"090300",
                					"address":"花园府北门西150米",
                					"distance":168,
                					"name":"明月桥路诊所",
                					"location":"120.219287,30.302572",
                					"id":"B0JRT6VKO7",
                					"type":"医疗保健服务;诊所;诊所"
                				},
                				{
                					"typecode":"090300",
                					"address":"环站北路与同协南路交叉口西180米",
                					"distance":235,
                					"name":"丰众诊所",
                					"location":"120.220567,30.303927",
                					"id":"B0FFJBXI6U",
                					"type":"医疗保健服务;诊所;诊所"
                				}
                			],
                			"middleSchool":[],
                			"shopping":[
                				{
                					"typecode":"060305",
                					"address":"明月桥路365号",
                					"distance":219,
                					"name":"中国移动(德信浙旅·东宸店)",
                					"location":"120.220240,30.299911",
                					"id":"B0GUGMZA74",
                					"type":"购物服务;家电电子卖场;手机销售"
                				},
                				{
                					"typecode":"060000",
                					"address":"环站北路笕东嘉苑底商22号",
                					"distance":230,
                					"name":"华顺开锁换锁配钥匙",
                					"location":"120.219973,30.303765",
                					"id":"B0GRU575F5",
                					"type":"购物服务;购物相关场所;购物相关场所"
                				},
                				{
                					"typecode":"060705",
                					"address":"笕东嘉苑1幢底商18-1号",
                					"distance":233,
                					"name":"明康汇生鲜菜市(笕东嘉苑店)",
                					"location":"120.220588,30.303911",
                					"id":"B0HARM1ZJL",
                					"type":"购物服务;综合市场;蔬菜市场"
                				},
                				{
                					"typecode":"060603",
                					"address":"钱塘白石嘉苑3幢3号(明石路地铁站C1口步行330米)",
                					"distance":273,
                					"name":"世泽五金店",
                					"location":"120.223641,30.302021",
                					"id":"B0FFIY6DH1",
                					"type":"购物服务;家居建材市场;建材五金市场"
                				},
                				{
                					"typecode":"060000",
                					"address":"东亚新干线1幢1单元2层",
                					"distance":285,
                					"name":"澈子映画(东站店)",
                					"location":"120.221365,30.299301",
                					"id":"B0J2YRKNF5",
                					"type":"购物服务;购物相关场所;购物相关场所"
                				},
                				{
                					"typecode":"060200",
                					"address":"明月桥路330号",
                					"distance":320,
                					"name":"美宜佳(明月桥路店)",
                					"location":"120.221419,30.298990",
                					"id":"B0HUBCENUT",
                					"type":"购物服务;便民商店/便利店;便民商店/便利店"
                				}
                			]
                		},
                		"districtId":330102,
                		"cityName":"杭州市",
                		"formattedAddress":"浙江省杭州市上城区笕桥街道花园府",
                		"name":"花园府",
                		"location":"120.220804,30.301819",
                		"id":210317,
                		"provinceName":"浙江省",
                		"longitude":120.220804
                	},
                	"labels":[
                		"拎包入住"
                	],
                	"coverUrl":"https://fyoss.fangyou.com/25031019a8f0812b9d4401a7e1a85e31d0303c8e2dab9497.jpg",
                	"lookType":"随时可看",
                	"medias":[
                		{
                			"subtype":"实景图",
                			"type":"IMAGE",
                			"url":"https://fyoss.fangyou.com/25031019a8f0812b9d4401a7e1a85e31d0303c8e2dab9497.jpg"
                		}
                	],
                	"channelDelegationMap":{
                		"ALIPAY":{
                			"businessQuantity":100,
                			"freeze":false,
                			"remainQuantity":99,
                			"status":"WITHOUT"
                		},
                		"XIANYU":{
                			"businessQuantity":100,
                			"outNickName":"说不出再见528995",
                			"freeze":false,
                			"remainQuantity":100,
                			"status":"WITHOUT"
                		},
                		"PRIVATE":{
                			"businessQuantity":100,
                			"freeze":false,
                			"remainQuantity":100,
                			"status":"WITHOUT"
                		}
                	},
                	"companyId":9095439084640331000,
                	"viewImage":[
                		{
                			"id":"https://fyoss.fangyou.com/25031019a8f0812b9d4401a7e1a85e31d0303c8e2dab9497.jpg",
                			"url":"https://fyoss.fangyou.com/25031019a8f0812b9d4401a7e1a85e31d0303c8e2dab9497.jpg"
                		}
                	],
                	"toiletCount":1,
                	"totalFloor":9,
                	"hallCount":1,
                	"subType":"RENT_FULL",
                	"currentFloor":3,
                	"brokerName":"王涛",
                	"status":"INIT",
                	"listDate":"2025-03-10",
                	"subTypeName":"整租",
                	"code":"359418bf987449dbb4aed4e6918609f5",
                	"typeName":"租房",
                	"description":"大城市里打拼，渴望一个温暖的港湾？花园府这套房子就是为你准备的。拎包即住，温馨舒适，仿佛家的拥抱。交通便利，周边设施齐全，让你的生活更加便捷无忧。中介提供贴心接送服务，看房不折腾，生活更幸福。期待你成为这里的主人，开启美好新生活。",
                	"remark":"",
                	"redo":"精装",
                	"title":"花园府 1室 1厅 1卫 80平 精装",
                	"around":[
                		"公交",
                		"学校",
                		"医院",
                		"购物",
                		"餐饮"
                	],
                	"isSync":true,
                	"communityAddress":"浙江省杭州市上城区明月桥路与花园兜街交叉口东北120米",
                	"floorCategory":"低楼层",
                	"cityName":"杭州市",
                	"priceTotal":8000,
                	"propertyType":"住宅",
                	"deadline":"2025-06-10 19:03:42",
                	"channelUp":true,
                	"parkingRatio":"1:0.42",
                	"buildName":"1栋",
                	"propertyYears":"70",
                	"districtName":"上城区",
                	"companyIdStr":"9095439084640330753",
                	"houseType":"商品房",
                	"propertyManagementCompany":"浙江保亿物业服务有限公司",
                	"broker":{
                		"code":"068f030413b14c238e7e112001c71a8e",
                		"name":"王涛",
                		"icon":"https://fyoss.fangyou.com/jpg_24110509c0a9aa084307dbbb8f1e2b06b0451d31b31f4df5",
                		"company":"杭州多友房地产经纪有限公司",
                		"storeName":"多友房",
                		"id":"9094675390735100677"
                	},
                	"parentId":"*********",
                	"mediasMap":{
                		"户型图":[],
                		"小区图片":[],
                		"实景图":[
                			"https://fyoss.fangyou.com/25031019a8f0812b9d4401a7e1a85e31d0303c8e2dab9497.jpg"
                		]
                	},
                	"equipments":[
                		"热水器",
                		"燃气灶",
                		"空调",
                		"冰箱",
                		"衣柜"
                	],
                	"payMonths":3,
                	"floorImage":[],
                	"depositMonths":1,
                	"tagElevator":false,
                	"brokerService":[],
                	"channelCodes":[
                		"ALIPAY"
                	]
                }""", EditDelegationPO.class));
    }

    @Test
    public void addDelegation() throws InterruptedException {
        ApiResponse<DelegationDTO> response = delegationApi.edit(1741958112698L,
                new EditDelegationPO()
                        .setType(RENT)
//                        .setSourceType("SAAS")
//                        .setSourceId(System.currentTimeMillis() + "")
//                        .setId(49953L)
//                        .setLevel(COMPANY)

//                        .setParentId(200852L)
                        .setLevel(BROKER)
//                        .setParentSourceId("f2992abf-e41d-4917-bf86-565a203a61b4")
                        .setCommunityAddress("上海浦东")
                        .setCommunityName("海上风华")
//                        .setCommunityName("357809")
//                        .setFreshCompanyDelegation(true)

//                        .setLevel(DelegationDTO.Level.CHANNEL)
                        .setChannelCodes(List.of(ChannelDTO.Code.PRIVATE, XIANYU))
//                        .setChannelCodes(List.of(ChannelDTO.Code.PRIVATE, ChannelDTO.Code.ALIPAY))
                        .setChannelUp(true)
//                        .setTitle("测试房源_租房_" + System.currentTimeMillis())
                        .setLabels(List.of("满二"))
                        .setMedias(getMedias())
                        .setRedo("普装")
                        .setCurrentFloor(1)
                        .setTotalFloor(10)
                        .setTagElevator(true)
                        .setCompletionTime("2022")
                        .setPropertyType("别墅")
                        .setElevatorCount(1)
                        .setRoomPerFloor(4)
                        .setMetro("近地铁")
                        .setSchool("近学校")
                        .setParkingRatio("1:2")
                        .setPropertyManagementCompany("物业公司")
                        .setPropertyManagementFee(new BigDecimal("2.2"))
//                        .setPriceUnit(new BigDecimal("10000"))
                        .setPriceTotal(new BigDecimal("1000000"))
                        .setDeadline(now().plusDays(90))
                        .setBuildName("10号楼")
                        .setUnitName("1单元")
                        .setRoomNum("101")
                        .setOrient("东西")
                        .setRoomCount(2)
                        .setHallCount(2)
                        .setToiletCount(2)
                        .setBuildingArea(new BigDecimal("45"))
                        .setSubType(DelegationDTO.SubType.RENT_FULL)
                        .setRoomType("主卧")
                        .setBailorCertNO("12345678901")
                        .setBailorCertType("身份证")
                        .setBailorNames("张三, 李四, 王五 ,赵六")
                        .setDeadline(now().plusDays(90))
                        .setHouseBusiNO("20231001")
                        .setHouseCertType("房产证")
                        .setHouseCertNO("20231001")
                        .setHousePlanPurpose("住宅")
                        .setOwnerName("张三")
                        .setOwnerCertNO("12345678901")
                        .setBailorName("李四")
                        .setEquipments(List.of("大阳台", "暖气", "热水器"))
                        .setPayMonths(3)
                        .setDepositMonths(1)
                        .setRoomName("101房间")
                        .setCheckinDate(LocalDate.now().plusDays(7))
                        .setAround(List.of("公交", "地铁房", "超市"))
                        .setUseArea(new BigDecimal("100"))
                        .setCompletionTime("2014")
                        .setListDate(LocalDate.now())
                        .setPropertyYears("70")
                        .setHouseCertVerify(true)
                        .setHouseStructure("平层")
                        .setPropertyOwnership("共有产权")
                        .setBuildingCategory("低层")
                        .setHouseYears("满二")
                        .setBuildingType("双拼")
                        .setParking(true)
                        .setLookType("周末可看")
                        .setSaleReason("刚改")
                        .setHouseSituation("自住")
                        .setHouseCertAddress("上海市静安区")
                        .setSoleHouse(true)
                        .setBrokerService(List.of("中介费特惠", "到店送礼", "十年房产经验"))
                        .setGovContractCode("20231001")
                        .setGovPromoCode("20231001")
                        .setGovVerifyCode("20231001")
                        .setGovVerifyUrl("http://www.baidu.com")

        );
        System.out.println(JSON.toJSONString(response, true));

//        sleep(5 * 60 * 1000L);
    }

    @Test
    public void receiveChannelNotify() {
        ApiResponse<DelegationDTO> response = delegationApi.receiveChannelNotify(252361L, DOWN_SUCC, "测试");
        System.out.println(JSON.toJSONString(response, true));
    }

    @Test
    public void upDown() throws InterruptedException {
        ApiResponse<DelegationVO> response = delegationApi.upDown(6052L, List.of(XIANYU), true, "测试");
        System.out.println(JSON.toJSONString(response, true));
        sleep(5 * 60 * 1000L);
    }

    @Test
    public void deleteBrokerDelegates() {
        ApiResponse<?> response = delegationApi.deleteBrokerDelegates(null, "9094762065924456967", "手动核销删除");
        System.out.println(JSON.toJSONString(response, true));
    }

    @SneakyThrows
    @Test
    public void manualPush() {
        ApiResponse<?> response = delegationApi.manualPush(5, 10, Integer.MAX_VALUE);
        System.out.println(JSON.toJSONString(response, true));
//        sleep(5 * 60 * 1000L);
    }

    @Test
    public void rebindCommunity() {
        delegationApi.rebindCommunity();
    }

}