package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.region.RegionWeb;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.ejuetc.consumer.api.dto.RegionDTO;

import static com.ejuetc.consumer.api.dto.RegionDTO.FeatureCode.HOUSE_VALUE_ASSESSMENT;
import static com.ejuetc.consumer.api.dto.RegionDTO.Type.*;


@RunWith(SpringRunner.class)
@SpringBootTest
public class RegionTest {

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    @Autowired
    private RegionApiImpl regionAPI;

    @Autowired
    private RegionWebImpl regionWeb;
//    private RegionWeb regionWeb = getAPI(RegionWeb.class, "http://localhost:8097");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void testRegion() {
        ApiResponse<?> response = regionWeb.businessOpenCities();
        System.out.println(JSON.toJSONString(response, true));
    }

//    @Test
//    public void testInitTown() {
//        List.of(
//                "441900"
//        ).forEach(cityCode -> {
//            try {
//                regionAPI.initTown(cityCode);
//            } catch (Exception e) {
//                System.out.println("error cityCode:" + cityCode);
//                e.printStackTrace();
//            }
//        });
//    }

    @Test
    public void testWebList() {
        // 先测试查询所有城市
        System.out.println("=== 测试1: 查询所有城市 ===");
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, null, CITY, HOUSE_VALUE_ASSESSMENT, null, null);
        System.out.println("所有城市查询结果:");
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

        // 再测试查询支持功能的城市 - 使用null参数
        System.out.println("\n=== 测试2: 查询支持功能的城市(使用null) ===");
        try {
            // 由于枚举问题，我们先用null测试基本功能
            ApiResponse<Object> response = regionWeb.list(null, null, CITY, null, null, null);
            System.out.println("功能查询结果:");
            System.out.println(JSON.toJSONString(response, true));
        } catch (Exception e) {
            System.out.println("查询出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
