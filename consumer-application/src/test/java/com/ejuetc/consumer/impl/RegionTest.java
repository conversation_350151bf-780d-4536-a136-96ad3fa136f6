package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.RegionDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static com.ejuetc.consumer.api.dto.RegionDTO.Type.*;

@SpringBootTest
public class RegionTest {

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    @Autowired
    private RegionApiImpl regionAPI;

    @Autowired
    private RegionWebImpl regionWeb;
//    private RegionWeb regionWeb = getAPI(RegionWeb.class, "http://localhost:8097");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void testRegion() {
        ApiResponse<?> response = regionWeb.businessOpenCities();
        System.out.println(JSON.toJSONString(response, true));
    }

//    @Test
//    public void testInitTown() {
//        List.of(
//                "441900"
//        ).forEach(cityCode -> {
//            try {
//                regionAPI.initTown(cityCode);
//            } catch (Exception e) {
//                System.out.println("error cityCode:" + cityCode);
//                e.printStackTrace();
//            }
//        });
//    }

    @Test
    public void testWebList() {
        // 先测试查询所有城市
        System.out.println("=== 测试1: 查询所有城市 ===");
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, null, CITY, null, null, null);
        System.out.println("所有城市查询结果:");
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

        // 再测试查询支持功能的城市
        System.out.println("\n=== 测试2: 查询支持HOUSE_VALUE_ASSESSMENT功能的城市 ===");
        try {
            // 使用反射来获取枚举值，避免编译时的枚举问题
            Class<?> featureCodeClass = Class.forName("com.ejuetc.consumer.api.dto.RegionDTO$FeatureCode");
            Object houseValueAssessment = Enum.valueOf((Class<Enum>) featureCodeClass, "HOUSE_VALUE_ASSESSMENT");

            // 获取GroupType类
            Class<?> groupTypeClass = Class.forName("com.ejuetc.consumer.web.region.RegionWeb$GroupType");

            // 调用list方法，传入功能编码
            java.lang.reflect.Method listMethod = regionWeb.getClass().getMethod("list",
                Long.class, Long.class, RegionDTO.Type.class, featureCodeClass, String.class, groupTypeClass);
            ApiResponse<Object> response = (ApiResponse<Object>) listMethod.invoke(regionWeb,
                null, null, CITY, houseValueAssessment, null, null);

            System.out.println("支持HOUSE_VALUE_ASSESSMENT功能的城市查询结果:");
            System.out.println(JSON.toJSONString(response, true));
        } catch (Exception e) {
            System.out.println("查询出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
