package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.region.RegionWeb;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.ejuetc.consumer.api.dto.RegionDTO;

import static com.ejuetc.consumer.api.dto.RegionDTO.Type.*;


@RunWith(SpringRunner.class)
@SpringBootTest
public class RegionTest {

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    @Autowired
    private RegionApiImpl regionAPI;

    @Autowired
    private RegionWebImpl regionWeb;
//    private RegionWeb regionWeb = getAPI(RegionWeb.class, "http://localhost:8097");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void testRegion() {
        ApiResponse<?> response = regionWeb.businessOpenCities();
        System.out.println(JSON.toJSONString(response, true));
    }

//    @Test
//    public void testInitTown() {
//        List.of(
//                "441900"
//        ).forEach(cityCode -> {
//            try {
//                regionAPI.initTown(cityCode);
//            } catch (Exception e) {
//                System.out.println("error cityCode:" + cityCode);
//                e.printStackTrace();
//            }
//        });
//    }

    @Test
    public void testWebList() {
        ApiResponse<Object> response = regionWeb.list(null, null, CITY, RegionDTO.FeatureCode.HOUSE_VALUE_ASSESSMENT, null, RegionWeb.GroupType.LETTER);
        System.out.println(JSON.toJSONString(response, true));
    }
}
