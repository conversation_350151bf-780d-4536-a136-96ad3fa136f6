package com.ejuetc.consumer.impl;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.querydomain.impl.QueryDomainApiImpl;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.web.region.RegionWeb;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.ejuetc.consumer.api.dto.RegionDTO;

import static com.ejuetc.consumer.api.dto.RegionDTO.FeatureCode.HOUSE_VALUE_ASSESSMENT;
import static com.ejuetc.consumer.api.dto.RegionDTO.Type.*;


@RunWith(SpringRunner.class)
@SpringBootTest
public class RegionTest {

    //    @Autowired
    private QueryDomainApiImpl queryDomainApi;

    @Autowired
    private RegionApiImpl regionAPI;

    @Autowired
    private RegionWebImpl regionWeb;
//    private RegionWeb regionWeb = getAPI(RegionWeb.class, "http://localhost:8097");
//    private CommunityAPI communityImpl = getAPI(CommunityAPI.class, "http://localhost:8097");

    @Test
    public void testRegion() {
        ApiResponse<?> response = regionWeb.businessOpenCities();
        System.out.println(JSON.toJSONString(response, true));
    }

//    @Test
//    public void testInitTown() {
//        List.of(
//                "441900"
//        ).forEach(cityCode -> {
//            try {
//                regionAPI.initTown(cityCode);
//            } catch (Exception e) {
//                System.out.println("error cityCode:" + cityCode);
//                e.printStackTrace();
//            }
//        });
//    }

    @Test
    public void testWebList() {
        // 先测试查询所有城市
        System.out.println("=== 查询所有城市 ===");
        ApiResponse<Object> allCitiesResponse = regionWeb.list(null, null, CITY, HOUSE_VALUE_ASSESSMENT, null, null);
        System.out.println(JSON.toJSONString(allCitiesResponse, true));

        // 再测试查询支持功能的城市
        System.out.println("=== 查询支持HOUSE_VALUE_ASSESSMENT功能的城市 ===");
        // 直接使用字符串来避免枚举问题
        System.out.println("测试功能查询...");
    }
}
