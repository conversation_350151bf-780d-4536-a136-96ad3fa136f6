package com.ejuetc.consumer.impl;

import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.dto.RegionDTO;
import com.ejuetc.consumer.domain.region.Region;
import com.ejuetc.consumer.domain.region.RegionRpt;
import com.ejuetc.consumer.web.region.RegionWeb;
import com.ejuetc.consumer.web.vo.RegionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static java.util.Comparator.comparing;

@RequiredArgsConstructor
@Slf4j
@RestController
@Transactional(rollbackFor = Exception.class)
public class RegionWebImpl implements RegionWeb {
    private final RegionRpt regionRpt;

    public void refreshPinyin() {
        regionRpt.findAll().forEach(Region::refreshPinyin);
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<List<RegionVO>> businessOpenCities() {
        List<Region> regions = regionRpt.findByBusinessOpen();
        return succ(convert2DTO(regions, new RegionVO()));
    }

    @Override
    @Transactional(readOnly = true)
    public ApiResponse<Object> list(Long parentRegionId, Long parentCityId, RegionDTO.Type type, RegionDTO.FeatureCode featureCode, String keyword, GroupType groupType) {
        List<Region> regions = regionRpt.findList(parentRegionId, parentCityId, type, keyword, featureCode);
        List<RegionVO> vos = convert2DTO(regions, new RegionVO());
        if (groupType != null) {
            Map<Object, List<RegionVO>> map = new TreeMap<>();
//            vos.sort(comparing(RegionVO::getPinyinInitials));
            for (RegionVO vo : vos) {
                map.computeIfAbsent(switch (groupType) {
                            case LETTER -> vo.getPinyinFirst();
                            case TYPE -> vo.getType();
                        },
                        k -> new ArrayList<>()).add(vo);
            }
            return succ(map);
        } else {
            return succ(vos);
        }
    }

}
